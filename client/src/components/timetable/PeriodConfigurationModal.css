/* Period Configuration Modal Styles */
.period-config-modal {
  max-width: 900px;
  width: 90vw;
  max-height: 90vh;
  overflow-y: auto;
}

.period-config-modal .modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid #e5e7eb;
  background: #f9fafb;
}

.period-config-modal .modal-header h2 {
  margin: 0;
  color: #1f2937;
  font-size: 1.5rem;
  font-weight: 600;
}

.period-config-modal .version-info {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.period-config-modal .version-badge {
  background: #e5e7eb;
  color: #374151;
  padding: 0.25rem 0.75rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
}

.period-config-modal .active-badge {
  background: #10b981;
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.375rem;
  font-size: 0.75rem;
  font-weight: 600;
}

.period-config-modal .modal-body {
  padding: 1.5rem;
  max-height: calc(90vh - 140px);
  overflow-y: auto;
}

.config-section {
  margin-bottom: 2rem;
}

.config-section h3 {
  margin: 0 0 1rem 0;
  color: #1f2937;
  font-size: 1.25rem;
  font-weight: 600;
  border-bottom: 2px solid #3b82f6;
  padding-bottom: 0.5rem;
}

.config-form .form-row {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 1rem;
  margin-bottom: 1rem;
}

.config-form .form-group {
  display: flex;
  flex-direction: column;
}

.config-form .form-group label {
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.config-form .form-group input {
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.config-form .form-group input:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.config-form .form-group input:disabled {
  background-color: #f3f4f6;
  color: #6b7280;
  cursor: not-allowed;
}

.config-form .form-help {
  font-size: 0.75rem;
  color: #6b7280;
  margin-top: 0.25rem;
}

.config-form .checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
  font-weight: 500;
}

.config-form .checkbox-label input[type="checkbox"] {
  width: auto;
  margin: 0;
}

.form-actions {
  margin-top: 1.5rem;
  padding-top: 1rem;
  border-top: 1px solid #e5e7eb;
}

.preview-section {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 0.5rem;
  padding: 1.5rem;
  margin-top: 2rem;
}

.preview-section h3 {
  margin: 0 0 1rem 0;
  color: #1e293b;
  font-size: 1.125rem;
  font-weight: 600;
}

.generation-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.stat-item {
  background: white;
  padding: 1rem;
  border-radius: 0.375rem;
  border: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.stat-label {
  font-weight: 500;
  color: #64748b;
  font-size: 0.875rem;
}

.stat-value {
  font-weight: 700;
  color: #1e293b;
  font-size: 1.125rem;
}

.periods-timeline {
  background: white;
  border-radius: 0.5rem;
  border: 1px solid #e2e8f0;
  overflow: hidden;
}

.timeline-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  border-bottom: 1px solid #f1f5f9;
}

.timeline-item:last-child {
  border-bottom: none;
}

.timeline-item.lecture-period {
  background: #f0f9ff;
  border-left: 4px solid #3b82f6;
}

.timeline-item.break-period {
  background: #fef3c7;
  border-left: 4px solid #f59e0b;
}

.time-range {
  font-weight: 600;
  color: #1e293b;
  font-size: 0.875rem;
  min-width: 120px;
}

.period-info {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 0.25rem;
}

.period-info strong {
  color: #1e293b;
  font-size: 0.875rem;
}

.period-info .duration {
  color: #64748b;
  font-size: 0.75rem;
  font-weight: 500;
}

.wef-modal {
  max-width: 500px;
}

.wef-explanation {
  background: #fef3c7;
  border: 1px solid #f59e0b;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-bottom: 1.5rem;
}

.wef-explanation p {
  margin: 0;
  color: #92400e;
  font-size: 0.875rem;
}

.version-control-info {
  background: #f0f9ff;
  border: 1px solid #3b82f6;
  border-radius: 0.375rem;
  padding: 1rem;
  margin-top: 1rem;
}

.version-control-info h4 {
  margin: 0 0 0.5rem 0;
  color: #1e40af;
  font-size: 0.875rem;
  font-weight: 600;
}

.version-control-info ul {
  margin: 0;
  padding-left: 1.25rem;
  color: #1e40af;
  font-size: 0.875rem;
}

.version-control-info li {
  margin-bottom: 0.25rem;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .period-config-modal {
    width: 95vw;
    max-height: 95vh;
  }
  
  .config-form .form-row {
    grid-template-columns: 1fr;
  }
  
  .generation-stats {
    grid-template-columns: 1fr;
  }
  
  .timeline-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .period-info {
    align-items: flex-start;
    width: 100%;
  }
}

/* Animation for modal appearance */
.period-config-modal {
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Loading state styles */
.period-config-modal .btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.period-config-modal .btn.loading {
  position: relative;
}

.period-config-modal .btn.loading::after {
  content: '';
  position: absolute;
  width: 16px;
  height: 16px;
  margin: auto;
  border: 2px solid transparent;
  border-top-color: currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
